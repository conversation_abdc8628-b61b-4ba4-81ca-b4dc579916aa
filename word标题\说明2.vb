'标题：章1章、1.1、1.11、（1）、a.编号
Sub ChangeParagraphStyle()
    ' 重置大纲编号库中的第5个编号模板
    ListGalleries(wdOutlineNumberGallery).Reset (5) ' 重置第5个大纲编号模板，避免旧设置影响
    '====================标题1：编号+样式设置====================
    With ListGalleries(wdOutlineNumberGallery).ListTemplates(5).ListLevels(1)
        .NumberFormat = "第%1章 "         ' 设置编号格式为“第X章 ”
        .NumberStyle = wdListNumberStyleArabic ' 设置编号样式为阿拉伯数字
        .NumberPosition = CentimetersToPoints(0) ' 编号左缩进为0厘米
        .Alignment = wdListLevelAlignCenter    ' 编号居中对齐
        .TextPosition = CentimetersToPoints(0) ' 标题文本缩进为0厘米
        .TabPosition = wdUndefined             ' 制表位未定义
        .ResetOnHigher = 0                    ' 不受更高级别影响
        .StartAt = 1                          ' 从1开始编号
        .LinkedStyle = "标题 1"               ' 关联到“标题 1”样式
    End With
    With ActiveDocument.Styles("标题 1").Font
        .NameFarEast = "黑体"                 ' 中文字体为黑体
        .NameAscii = "黑体"                   ' 英文及数字字体为黑体
        .NameOther = "黑体"                   ' 其他语言字体为黑体
        .Name = "黑体"                        ' 字体名称
        .Size = 15                            ' 字号为15磅（小三）
        .Bold = True                          ' 加粗
        .Color = wdColorBlack                 ' 字体颜色为黑色
    End With
    With ActiveDocument.Styles("标题 1").ParagraphFormat
        .Alignment = wdAlignParagraphCenter   ' 段落居中对齐
        .SpaceBefore = 24                     ' 段前24磅
        .SpaceAfter = 18                      ' 段后18磅
        .RightIndent = 0                      ' 右缩进0
    End With
    ' ====================标题2：编号+样式设置====================
    With ListGalleries(wdOutlineNumberGallery).ListTemplates(5).ListLevels(2)
        .NumberFormat = "%1.%2 "              ' 设置编号格式为“1.1 ”
        .NumberStyle = wdListNumberStyleArabic ' 设置编号样式为阿拉伯数字
        .NumberPosition = CentimetersToPoints(0) ' 编号左缩进为0厘米
        .Alignment = wdListLevelAlignLeft     ' 编号左对齐
        .TextPosition = CentimetersToPoints(0) ' 标题文本缩进为0厘米
        .TabPosition = wdUndefined            ' 制表位未定义
        .ResetOnHigher = 1                    ' 受更高级别影响
        .StartAt = 1                          ' 从1开始编号
        .LinkedStyle = "标题 2"               ' 关联到“标题 2”样式
    End With
    With ActiveDocument.Styles("标题 2").Font
        .NameFarEast = "黑体"                 ' 中文字体为黑体
        .NameAscii = "黑体"                   ' 英文及数字字体为黑体
        .NameOther = "黑体"                   ' 其他语言字体为黑体
        .Name = "黑体"                        ' 字体名称
        .Size = 14                            ' 字号为14磅（四号）
        .Bold = True                          ' 加粗
        .Color = wdColorBlack                 ' 字体颜色为黑色
    End With
    With ActiveDocument.Styles("标题 2").ParagraphFormat
        .Alignment = wdAlignParagraphLeft     ' 段落左对齐
        .SpaceBefore = 12                     ' 段前12磅
        .SpaceAfter = 6                       ' 段后6磅
        .RightIndent = 0                      ' 右缩进0
    End With
    ' ====================标题3：编号+样式设置====================
    With ListGalleries(wdOutlineNumberGallery).ListTemplates(5).ListLevels(3)
        .NumberFormat = "%1.%2.%3 "           ' 设置编号格式为“1.1.1 ”
        .NumberStyle = wdListNumberStyleArabic ' 设置编号样式为阿拉伯数字
        .NumberPosition = CentimetersToPoints(0.74) ' 编号左缩进0.74厘米（2字符）
        .Alignment = wdListLevelAlignLeft     ' 编号左对齐
        .TextPosition = CentimetersToPoints(0.74) ' 标题文本缩进0.74厘米
        .TabPosition = wdUndefined            ' 制表位未定义
        .ResetOnHigher = 2                    ' 受更高级别影响
        .StartAt = 1                          ' 从1开始编号
        .LinkedStyle = "标题 3"               ' 关联到“标题 3”样式
    End With
    With ActiveDocument.Styles("标题 3").Font
        .NameFarEast = "宋体"                 ' 中文字体为宋体
        .NameAscii = "宋体"                   ' 英文及数字字体为宋体
        .NameOther = "宋体"                   ' 其他语言字体为宋体
        .Name = "宋体"                        ' 字体名称
        .Size = 12                            ' 字号为12磅（小四）
        .Bold = True                          ' 加粗
        .Color = wdColorBlack                 ' 字体颜色为黑色
    End With
    With ActiveDocument.Styles("标题 3").ParagraphFormat
        .Alignment = wdAlignParagraphLeft     ' 段落左对齐
        .SpaceBefore = 6                      ' 段前6磅
        .SpaceAfter = 3                       ' 段后3磅
        .RightIndent = 0                      ' 右缩进0
    End With
    ' ====================标题4：编号+样式设置====================
    With ListGalleries(wdOutlineNumberGallery).ListTemplates(5).ListLevels(4)
        .NumberFormat = "(%4) "                ' 设置编号格式为“(1) ”
        .NumberStyle = wdListNumberStyleArabic ' 设置编号样式为阿拉伯数字
        .NumberPosition = CentimetersToPoints(1.11) ' 编号左缩进1.48厘米（4字符）
        .Alignment = wdListLevelAlignLeft     ' 编号左对齐
        .TextPosition = CentimetersToPoints(2)    ' 标题文本缩进1.48厘米
        .TabPosition = wdUndefined            ' 制表位未定义
        .ResetOnHigher = 3                    ' 受更高级别影响
        .StartAt = 1                          ' 从1开始编号
        .LinkedStyle = "标题 4"               ' 关联到“标题 4”样式
    End With
    With ActiveDocument.Styles("标题 4").Font
        .NameFarEast = "宋体"                 ' 中文字体为宋体
        .NameAscii = "宋体"                   ' 英文及数字字体为宋体
        .NameOther = "宋体"                   ' 其他语言字体为宋体
        .Name = "宋体"                        ' 字体名称
        .Size = 12                            ' 字号为12磅（小四）
        .Bold = True                          ' 加粗
        .Color = wdColorBlack                 ' 字体颜色为黑色
    End With
    With ActiveDocument.Styles("标题 4").ParagraphFormat
        .Alignment = wdAlignParagraphLeft     ' 段落左对齐
        .SpaceBefore = 6                      ' 段前6磅
        .SpaceAfter = 6                       ' 段后6磅
        .RightIndent = 0                      ' 右缩进0
    End With
    ' ====================标题5：编号+样式设置====================
    With ListGalleries(wdOutlineNumberGallery).ListTemplates(5).ListLevels(5)
        .NumberFormat = "%5. "                ' 设置编号格式为“a. ”
        .NumberStyle = wdListNumberStyleLowercaseLetter ' 设置编号样式为小写字母
        .NumberPosition = CentimetersToPoints(1.48) ' 编号左缩进1.48厘米（6字符）
        .Alignment = wdListLevelAlignLeft     ' 编号左对齐
        .TextPosition = CentimetersToPoints(0) ' 标题文本缩进1.48厘米
        .TabPosition = wdUndefined            ' 制表位未定义
        .ResetOnHigher = 4                    ' 受更高级别影响
        .StartAt = 1                          ' 从a开始编号
        .LinkedStyle = "标题 5"               ' 关联到“标题 5”样式
    End With
    With ActiveDocument.Styles("标题 5").Font
        .NameFarEast = "宋体"                 ' 中文字体为宋体
        .NameAscii = "宋体"                   ' 英文及数字字体为宋体
        .NameOther = "宋体"                   ' 其他语言字体为宋体
        .Name = "宋体"                        ' 字体名称
        .Size = 12                            ' 字号为12磅（小四）
        .Bold = False                         ' 不加粗
        .Color = wdColorBlack                 ' 字体颜色为黑色
    End With
    With ActiveDocument.Styles("标题 5").ParagraphFormat
        .Alignment = wdAlignParagraphLeft     ' 段落左对齐
        .SpaceBefore = 0                      ' 段前0
        .SpaceAfter = 6                       ' 段后6磅
        .RightIndent = 0                      ' 右缩进0
    End With
    ' ====================标题6：编号+样式设置====================
    With ListGalleries(wdOutlineNumberGallery).ListTemplates(5).ListLevels(6)
        .NumberFormat = "%6 "                 ' 设置编号格式为“① ”
        .NumberStyle = wdListNumberStyleNumberInCircle ' 设置编号样式为带圈数字
        .NumberPosition = CentimetersToPoints(1.48) ' 编号左缩进1.48厘米
        .Alignment = wdListLevelAlignLeft     ' 编号左对齐
        .TextPosition = CentimetersToPoints(0) ' 标题文本缩进1.48厘米
        .TabPosition = wdUndefined            ' 制表位未定义
        .ResetOnHigher = 5                    ' 受更高级别影响
        .StartAt = 1                          ' 从①开始编号
        .LinkedStyle = "标题 6"               ' 关联到“标题 6”样式
    End With
    With ActiveDocument.Styles("标题 6").Font
        .NameFarEast = "宋体"                 ' 中文字体为宋体
        .NameAscii = "宋体"                   ' 英文及数字字体为宋体
        .NameOther = "宋体"                   ' 其他语言字体为宋体
        .Name = "宋体"                        ' 字体名称
        .Size = 12                            ' 字号为12磅（小四）
        .Bold = False                         ' 不加粗
        .Color = wdColorBlack                 ' 字体颜色为黑色
    End With
    With ActiveDocument.Styles("标题 6").ParagraphFormat
        .Alignment = wdAlignParagraphLeft     ' 段落左对齐
        .SpaceBefore = 0                      ' 段前0
        .SpaceAfter = 6                       ' 段后6磅
        .RightIndent = 0                      ' 右缩进0
    End With
    ' ========== 标题7 标题8 标题9==========
    Dim i As Integer ' 定义循环变量
    For i = 7 To 9 ' 循环设置7~9级标题编号样式
        With ListGalleries(wdOutlineNumberGallery).ListTemplates(5).ListLevels(i)
            .NumberFormat = "" ' 不设置编号格式
            .NumberStyle = wdListNumberStyleArabic ' 阿拉伯数字编号样式
        End With
    Next i


    ' 设置模板名称为空，避免影响
    ListGalleries(wdOutlineNumberGallery).ListTemplates(5).Name = ""
    ' 应用设置好的多级列表模板到选区
    Selection.Range.ListFormat.ApplyListTemplateWithLevel ListTemplate:= _
        ListGalleries(wdOutlineNumberGallery).ListTemplates(5), _
        ContinuePreviousList:=True, ApplyTo:=wdListApplyToWholeList, _
        DefaultListBehavior:=wdWord10ListBehavior
    ' ========== 正文样式设置 ==========
    With ActiveDocument.Styles("正文").Font ' 设置正文字体
        .NameFarEast = "宋体" ' 中文字体宋体
        .NameAscii = "宋体" ' 英文及数字字体宋体
        .NameOther = "宋体" ' 其他语言字体宋体
        .Name = "宋体" ' 字体名称
        .Size = 12 ' 字号12磅（小四号）
        .Bold = False ' 不加粗
        .Color = wdColorBlack ' 黑色
    End With
    With ActiveDocument.Styles("正文").ParagraphFormat ' 设置正文段落格式
        .Alignment = wdAlignParagraphLeft ' 段落左对齐
        .SpaceBefore = 0 ' 段前0磅
        .SpaceAfter = 6 ' 段后6磅
        .LineSpacingRule = wdLineSpaceMultiple ' 多倍行距
        .LineSpacing = 1.2 ' 1.2倍行距
        .FirstLineIndent = CentimetersToPoints(0.74) ' 首行缩进2字符（0.74厘米）
        .LeftIndent = 0 ' 左缩进0
        .RightIndent = 0 ' 右缩进0
    End With

    ' ========== 无间隔样式设置 ==========
    With ActiveDocument.Styles("无间隔").Font ' 设置无间隔样式字体
        .NameFarEast = "宋体" ' 中文字体宋体
        .NameAscii = "宋体" ' 英文及数字字体宋体
        .NameOther = "宋体" ' 其他语言字体宋体
        .Name = "宋体" ' 字体名称
        .Size = 12 ' 字号12磅（小四号）
        .Bold = False ' 不加粗
        .Color = wdColorBlack ' 黑色
    End With
    With ActiveDocument.Styles("无间隔").ParagraphFormat ' 设置无间隔样式段落格式
        .Alignment = wdAlignParagraphCenter ' 段落居中对齐
    End With

End Sub

